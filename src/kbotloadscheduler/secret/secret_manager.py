from functools import lru_cache
import os
import json
from dependency_injector.providers import Configuration
from google.cloud import secretmanager


class ConfigWithSecret(object):
    def __init__(self, config: Configuration) -> None:
        self.config = config
        self.gcp_project_id = self.config.get('gcp_project_id', '')
        self.secret_manager_client = None
        if self.config.get('env') != 'tests':
            self.secret_manager_client = secretmanager.SecretManagerServiceClient()

    def get_config(self) -> Configuration:
        return self.config

    def get_kbot_loadscheduler_client_id(self):
        key = self.config.get('kbot_loadscheduler_client_id')
        return self.retrieve_secret(key)

    def get_kbot_loadscheduler_client_secret(self):
        key = self.config.get('kbot_loadscheduler_client_secret')
        return self.retrieve_secret(key)

    def get_sharepoint_client_config(self, perimeter_code):
        key = perimeter_code + '-sharepoint-client-config'
        client_config = json.loads(self.retrieve_secret(key))
        return client_config

    def get_sharepoint_client_private_key(self, perimeter_code):
        key = perimeter_code + '-sharepoint-client-private-key'
        client_private_key = self.retrieve_secret(key)
        return client_private_key

    def get_basic_client_id(self):
        key = 'basic-client-id'
        return self.retrieve_secret(key)

    def get_basic_client_secret(self):
        key = 'basic-client-secret'
        return self.retrieve_secret(key)

    @lru_cache
    def retrieve_secret(self, secret_id):
        try:
            secret_value = self.retrieve_secret_from_file(secret_id)
        except OSError:
            secret_value = None
        if secret_value is None:
            secret_value = self.retrieve_secret_directly(secret_id)
        return secret_value

    def retrieve_secret_from_file(self, secret_id):
        secret_path = os.path.join(self.config.get('path_to_secret_config', '/etc/secrets'), secret_id, 'secret')
        if os.path.exists(secret_path):
            with open(secret_path) as f:
                content = f.read()
            return content.strip(" \n\r\t")
        return None

    def retrieve_secret_directly(self, secret_id):
        if self.secret_manager_client is None:
            return ''
        secret_resource_name = f"projects/{self.gcp_project_id}/secrets/{secret_id}/versions/latest"
        secret_version_response = self.secret_manager_client.access_secret_version(
            name=secret_resource_name
        )
        return secret_version_response.payload.data.decode("UTF-8").strip(" \n\r\t")
