from dependency_injector import containers, providers

from ..secret.secret_manager import ConfigWithSecret
from ..apicall.kbot_back_api import KbotBackApi
from ..apicall.kbot_embedding_api import Kbot<PERSON>mbedding<PERSON>pi
from ..gcs.treatment_file_manager import TreatmentFileManager
from ..loader.gcs.gcs_loader import Gcs<PERSON>oader
from ..loader.basic.basic_loader import BasicLoader
from ..loader.sharepoint.sharepoint_loader import SharepointLoader
from ..loader.loader_manager import LoaderManager
from ..service.schedule_service import ScheduleService
from ..service.sources_service import SourcesService
from ..service.loader_service import LoaderService
from ..service.document_service import DocumentService


class Container(containers.DeclarativeContainer):

    wiring_config = containers.WiringConfiguration(packages=["..route"])

    config = providers.Configuration()
    config.env.from_env('ENV', 'local')
    config.version.from_env('VERSION', 'dev')
    config.gcp_project_id.from_env('GCP_PROJECT_ID', '')
    # following default values are for unit tests launched from IDE
    config.path_to_secret_config.from_env('PATH_TO_SECRET_CONFIG', '')
    config.kbot_back_api_cloud_run_url.from_env('KBOT_BACK_API_CLOUD_RUN_URL', 'https://kbot-back-api:8080')
    config.kbot_back_url.from_env('KBOT_BACK_API_URL', 'https://kbot-back-api:8080')
    config.kbot_embedding_url.from_env('KBOT_EMBEDDING_API_URL', 'https://kbot-embedding-api:8081')
    config.kbot_work_bucket_prefix.from_env('KBOT_WORK_BUCKET_PREFIX', 'gs://mon_bucket-[perimeter_code]')
    config.okapi_url.from_env('OKAPI_URL', '')
    config.kbot_loadscheduler_client_id.from_env('KBOT_LOADSCHEDULER_CLIENT_ID', 'kbot_loadscheduler_client_id')
    config.kbot_loadscheduler_client_secret.from_env('KBOT_LOADSCHEDULER_CLIENT_SECRET',
                                                     'kbot_loadscheduler_client_secret')
    config.kbot_loadscheduler_client_scope.from_env('KBOT_LOADSCHEDULER_CLIENT_SCOPE', '')

    configWithSecret = providers.Singleton(ConfigWithSecret,
                                           config=config
                                           )

    kbot_back_api = providers.Factory(
        KbotBackApi,
        url=config.kbot_back_url,
        env=config.env,
        config_with_secret=configWithSecret
    )

    kbot_embedding_api = providers.Factory(
        KbotEmbeddingApi,
        url=config.kbot_embedding_url,
        env=config.env
    )

    treatment_file_manager = providers.Factory(
        TreatmentFileManager,
        base_bucket=config.kbot_work_bucket_prefix
    )

    gcs_loader = providers.Factory(GcsLoader)

    basic_loader = providers.Factory(BasicLoader,
                                     config_with_secret=configWithSecret)

    sharepoint_loader = providers.Factory(SharepointLoader, config=configWithSecret)

    loader_manager = providers.Factory(
        LoaderManager,
        gcs=gcs_loader,
        basic=basic_loader,
        sharepoint=sharepoint_loader
    )

    sources_service = providers.Factory(
        SourcesService,
        kbot_back_api=kbot_back_api,
        treatment_file_manager=treatment_file_manager
    )

    loader_service = providers.Factory(
        LoaderService,
        loader_manager=loader_manager,
        treatment_file_manager=treatment_file_manager
    )

    document_service = providers.Factory(
        DocumentService,
        kbot_embedding_api=kbot_embedding_api,
        treatment_file_manager=treatment_file_manager
    )

    schedule_service = providers.Factory(
        ScheduleService,
        kbot_back_api=kbot_back_api,
        treatment_file_manager=treatment_file_manager,
        sources_service=sources_service,
        document_service=document_service,
        loader_service=loader_service
    )
