from unittest.mock import MagicMock

from kbotloadscheduler.loader.gcs.gcs_loader import Gcs<PERSON>oader
from kbotloadscheduler.loader.basic.basic_loader import BasicLoader
from kbotloadscheduler.loader.confluence.confluence_loader import ConfluenceLoader
from kbotloadscheduler.loader.loader_manager import LoaderManager
from kbotloadscheduler.bean.beans import SourceBean
from testutils.mock_gcs import MockGcs
from data.gcs_repo_test_data import GcsRepoTestData
from data.basic_connect_mock import basic_connect_mock
from dependency_injector import providers

from src.kbotloadscheduler.secret.secret_manager import ConfigWithSecret


class TestLoaderManager:

    def test_confluence_loader_integration(self, mocker):
        """Test que le ConfluenceLoader est correctement intégré dans le LoaderManager"""
        # Mock du ConfigWithSecret
        config_with_secret = self.__get_config_with_secret__(mocker)

        # Mock de l'environnement pour ConfluenceLoader
        mocker.patch.dict('os.environ', {
            'CONFLUENCE_URL': 'https://test.atlassian.net'
        })

        # C<PERSON>er le LoaderManager avec seulement le ConfluenceLoader
        loader_manager = LoaderManager(
            confluence=ConfluenceLoader(config_with_secret)
        )

        # Vérifier que le ConfluenceLoader est accessible
        confluence_loader = loader_manager.get_loader("confluence")
        assert confluence_loader is not None
        assert isinstance(confluence_loader, ConfluenceLoader)
        assert confluence_loader._loader_type == "confluence"

    def test_get_document_list(self, mocker, requests_mock):
        my_mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")
        GcsRepoTestData.write_get_list(my_mock_gcs)
        GcsRepoTestData.prepare_gcs_repo(my_mock_gcs)

        # Here we won't use BasicLoader but has we create it, we must mock connections calls
        basic_connect_mock(requests_mock)

        loader_manager = LoaderManager(gcs=GcsLoader(), basic=BasicLoader(self.__get_config_with_secret__(mocker)))
        source_bean = SourceBean(**GcsRepoTestData.SOURCE)
        loader = loader_manager.get_loader(source_bean.src_type)
        actual_document_list = loader.get_document_list(source_bean)

        assert actual_document_list == GcsRepoTestData.get_document_list_for_source()

    def __get_config_with_secret__(self, mocker):
        config = providers.Configuration()
        config.env.from_env('ENV', 'local')
        config.gcp_project_id.from_env('GCP_PROJECT_ID', '')
        config_with_secret = ConfigWithSecret(config=config)
        mocker.patch.object(config_with_secret, "get_basic_client_id", MagicMock(return_value="fake-client-id"))
        mocker.patch.object(config_with_secret, "get_basic_client_secret", MagicMock(return_value="fake-client-pwd"))
        mocker.patch.object(config_with_secret, "get_confluence_credentials", MagicMock(return_value={"pat_token": "fake-pat-token"}))
        return config_with_secret
