# 🧪 Guide Complet des Tests Confluence

Ce guide présente tous les types de tests disponibles pour le module Confluence dans kbot-load-scheduler.

## 📋 Vue d'Ensemble

Le système de tests Confluence est organisé en **3 niveaux** :

1. **Tests d'Intégration Existants** (avec mocks) ✅
2. **Tests Avancés Adaptés** (unitaires détaillés) 🔄
3. **Tests d'Intégration avec Vraie Instance** (nouveaux) ✅

## 🎯 Quel Test Choisir ?

### Pour le Développement Quotidien
```bash
# Tests rapides avec mocks (recommandé)
pytest tests/loader/test_confluence_end_to_end.py -v
```

### Pour Valider avec une Vraie Instance
```bash
# Configuration une seule fois
python conf/etc/secrets/tests/setup_confluence_secrets.py --interactive

# Tests d'intégration rapides
cd tests/loader/confluence
python run_integration_tests.py --run-fast
```

### Pour les Tests Détaillés
```bash
# Tests unitaires adaptés
cd tests/loader/confluence
python run_adapted_tests.py
```

## 📁 Structure Complète

```
tests/loader/
├── test_confluence_end_to_end.py           # Tests d'intégration avec mocks ✅
├── test_confluence_integration_with_mock_gcs.py  # Tests GCS ✅
├── test_confluence_loader.py               # Tests du loader ✅
├── test_confluence_with_mock_confluence.py # Tests avec MockConfluence ✅
└── confluence/                             # Tests avancés
    ├── test_real_confluence_integration.py # Tests avec vraie instance ✅
    ├── test_performance_integration.py     # Tests de performance ✅
    ├── run_integration_tests.py           # Script d'intégration ✅
    ├── demo_integration.py                # Démonstration ✅
    ├── test_*_adapted.py                  # Tests unitaires adaptés 🔄
    ├── README.md                          # Vue d'ensemble
    └── README_INTEGRATION.md              # Guide d'intégration ✅

conf/etc/secrets/tests/
├── confluence-credentials/secret           # Secrets pour tests ✅
├── setup_confluence_secrets.py           # Configuration ✅
└── README_SECRETS.md                      # Documentation ✅
```

## 🚀 Démarrage Rapide par Cas d'Usage

### 🔧 Je développe une nouvelle fonctionnalité
```bash
# Tests rapides avec mocks
pytest tests/loader/test_confluence_end_to_end.py::TestConfluenceEndToEnd::test_get_document_list -v
```

### 🧪 Je veux tester avec une vraie instance
```bash
# Configuration (une seule fois)
python conf/etc/secrets/tests/setup_confluence_secrets.py --interactive

# Tests d'intégration
cd tests/loader/confluence
python run_integration_tests.py --health-check
python run_integration_tests.py --run-fast
```

### 📊 Je veux mesurer les performances
```bash
cd tests/loader/confluence
python run_integration_tests.py --run-performance
```

### 🎬 Je veux une démonstration
```bash
cd tests/loader/confluence
python demo_integration.py
```

### 🔍 Je veux des tests détaillés
```bash
cd tests/loader/confluence
python run_adapted_tests.py --all
```

## 🔐 Configuration des Secrets

### Système Hybride
- **Tests unitaires** : Utilisent des mocks (pas de secrets requis)
- **Tests d'intégration** : Utilisent le système de fichiers local (`conf/etc/secrets/tests/`)
- **Production** : Utilise Google Cloud Secret Manager

### Configuration Rapide
```bash
# Option 1 : Interactive (recommandée)
python conf/etc/secrets/tests/setup_confluence_secrets.py --interactive

# Option 2 : Depuis variables d'environnement
cp tests/loader/confluence/.env.example tests/loader/confluence/.env
# Éditer .env puis :
python conf/etc/secrets/tests/setup_confluence_secrets.py --from-env

# Vérification
python conf/etc/secrets/tests/setup_confluence_secrets.py --verify
```

## 📊 Comparaison des Types de Tests

| Type | Vitesse | Configuration | Réalisme | Usage |
|------|---------|---------------|----------|-------|
| **Tests avec Mocks** | ⚡ Très rapide | ✅ Aucune | 🟡 Simulé | Développement quotidien |
| **Tests d'Intégration** | 🐌 Moyen | 🔧 Secrets requis | ✅ Réel | Validation avant release |
| **Tests de Performance** | 🐌 Lent | 🔧 Secrets requis | ✅ Réel | Optimisation |
| **Tests Adaptés** | ⚡ Rapide | ✅ Aucune | 🟡 Simulé | Tests détaillés |

## 🎯 Recommandations par Rôle

### 👨💻 Développeur
1. **Quotidien** : Tests avec mocks (`pytest tests/loader/test_confluence_*.py`)
2. **Avant commit** : Tests d'intégration rapides (`--run-fast`)
3. **Avant PR** : Tests complets (`--run-all`)

### 🧪 QA/Testeur
1. **Configuration** : `python demo_integration.py`
2. **Tests fonctionnels** : `python run_integration_tests.py --run-all`
3. **Tests de performance** : `python run_integration_tests.py --run-performance`

### 🚀 DevOps/CI
1. **Pipeline rapide** : Tests avec mocks
2. **Pipeline complet** : Tests d'intégration avec secrets injectés
3. **Monitoring** : Tests de performance périodiques

## 📚 Documentation Détaillée

- **[README_INTEGRATION.md](README_INTEGRATION.md)** - Guide complet des tests d'intégration
- **[README_SECRETS.md](../../../conf/etc/secrets/tests/README_SECRETS.md)** - Système de secrets
- **[README.md](README.md)** - Tests adaptés et vue d'ensemble

## 🔄 Workflow Recommandé

### Première Configuration
```bash
# 1. Configurer les secrets
python conf/etc/secrets/tests/setup_confluence_secrets.py --interactive

# 2. Vérifier la configuration
cd tests/loader/confluence
python run_integration_tests.py --check-config

# 3. Test de connexion
python run_integration_tests.py --health-check
```

### Développement Quotidien
```bash
# Tests rapides avec mocks
pytest tests/loader/test_confluence_end_to_end.py -v

# Si besoin de validation réelle
cd tests/loader/confluence
python run_integration_tests.py --run-fast
```

### Avant Release
```bash
# Tests complets
cd tests/loader/confluence
python run_integration_tests.py --run-all
python run_integration_tests.py --run-performance
```

## 🆘 Support et Dépannage

### Problèmes Courants
1. **"Configuration non disponible"** → Voir [./README_INTEGRATION.md](README_INTEGRATION.md#-dépannage)
2. **"Health check échoué"** → Vérifier URL et token
3. **"Aucun document trouvé"** → Vérifier l'espace de test
4. **Tests lents** → Utiliser `--run-fast`

### Ressources
- Documentation complète dans `confluence/README_INTEGRATION.md`
- Démonstration interactive : `python confluence/demo_integration.py`
- Configuration des secrets : `python conf/etc/secrets/tests/setup_confluence_secrets.py --help`
